{"applicationStatus": "inProgress", "consentToDataProtection": true, "birthDate": "1997-02-08", "lastName": "<PERSON><PERSON><PERSON>", "localization": "en", "passedEnglishTest": "Yes", "ukStudyDurationDisplayName": "2", "contactNumber": {"number": "7238943243", "numberWithCode": "+917238943243", "dialCode": "91", "countryCode": "in"}, "priorLearningDetails": "ewfew", "opportunityApplicationSource": "Agent Portal", "passportNumber": "P4157831B", "englishMediumYears": "1", "finalDeclaration": true, "locationDisplayName": "London Stratford", "intakeDisplayName": "May 2026", "program": "a01UC00000HrRQ8YAN", "passportIssueDate": "2019-12-09", "SK": "ARD_0f86293b-8d8e-483c-aafa-ec82e3a3de48", "previousLastName": "ladst", "agentContactUserId": "FSFEWF", "productId": "01tUC000008SMLxYAO", "legalNationalityDisplayName": "British National", "location": "London Stratford", "ukHighestStudyDetails": "dvcx", "visaType": "other", "ocrReprocessCount": 11, "applicantInitials": "LN", "fullName": "<PERSON>", "proofOfAddress": [{"oapName": "ARD", "bucketName": "reviewcenter-stage", "documentType": "Proof of Address", "documentName": "438350db-9129-461f-9d3c-aec2bb4017a7.pdf", "path": "0f86293b-8d8e-483c-aafa-ec82e3a3de48/Proof of Address/f7182a85-4619-4084-9f1d-6a3d4f95fca2.pdf", "createdAt": "2025-07-15T19:24:41.812Z", "businessUnitFilter": "ARD", "SK": "ARD_0f86293b-8d8e-483c-aafa-ec82e3a3de48_Proof of Address_f7182a85-4619-4084-9f1d-6a3d4f95fca2", "documentId": "f7182a85-4619-4084-9f1d-6a3d4f95fca2", "documentFormat": "pdf", "PK": "<EMAIL>", "applicationId": "0f86293b-8d8e-483c-aafa-ec82e3a3de48", "contentType": "application/pdf", "email": "<EMAIL>"}], "agentAccountId": "EBJFWE", "englishCertificateUpload": [{"documentId": "02a2e559-5fc1-4ebb-9320-795f1d589e02"}], "isIdentityInformationAccurate": "Yes", "correspondencePostalCode": "607106", "pricebookId": "01sUC000001k9qvYAA", "courseDiscovery": "Referral Programme", "agentContactId": "DGGDS", "passportName": "Lucia", "correspondenceCounty": "Tamil Nadu", "intake": "2026-05-01", "accountManagerUserId": "SFJHSF", "correspondenceCountry": "AX", "programDisplayName": "BA (Hons) Business Management (Top-Up)", "englishQualificationType": "fewf", "ethinicBackground": "Asian - Filipino", "email": "<EMAIL>", "hasPersonalStatement": "Yes", "leadSource": "Agent", "firstName": "Lucia", "requireStudentVisa": "Yes", "dualNationality": "Antigua and Barbuda", "ukStudyDuration": "two", "disabilityDisplayName": "Social/communication conditions such as a speech and language impairment or an autistic spectrum condition", "admissionStage": "Draft Application", "passportExpiryDate": "2029-12-08", "correspondenceStreetAddress": "1102, voc nagar, ln puram", "visaTypeName": "nmz", "correspondenceCountryDisplayName": "Aland Islands", "placeOfBirth": "Bogo Cebu", "applicationFilledBy": "agent", "brand": "ARD", "gender": "Male", "updatedAt": "2025-07-15T19:46:30.981Z", "firstOfficialLanguage": "ewf", "haveCriminalConvictions": "Yes", "provideReferencesNow": "yes", "propertyName": "property name", "ocrExtractedResponse": {"passportNumber": "P4157831B", "firstName": "Lucia", "lastName": "<PERSON><PERSON><PERSON>", "placeOfBirth": "Bogo Cebu", "passportFirstName": "Lucia", "passportLastName": "<PERSON><PERSON><PERSON>", "passportIssueDate": "2019-12-09", "passportExpiryDate": "2029-12-08", "birthDate": "1997-02-08", "passportIssuingCountryDisplayName": "Philippines", "passportIssuingCountry": "PH"}, "courseDiscoveryDisplayName": "School or Careers Advisor", "isCorrespondanceAddressDiffer": "Yes", "isEnglishFirstLanguage": "No", "lastModifiedDetails": {"modifiedBy": "Agent", "modifiedOn": "2025-07-15T19:46:29.789Z"}, "country": "AF", "displayText": "May 2026", "previouslyAppliedOrStudiedInArden": "Yes", "businessUnit": "a0gUC000001iC4wYAE", "otherWorkExperience": "Yes", "opportunityRecordTypeId": "0120X0000005liVQAQ", "ukStudyVisaTypes": "saxas", "middleName": "mid", "privacyPolicyConsent": true, "stage": "Application", "disability": "Social/communication conditions such as a speech and language impairment or an autistic spectrum condition", "ethinicBackgroundDisplayName": "Asian - Filipino", "county": "Karnataka", "applicationRecordTypeId": "0120X0000001EiQQAU", "sectionLabel": "REVIEW_AND_SUBMIT", "businessUnitFilter": "ARD", "passportDocument": [{"documentId": "d44a98e7-69e1-4b37-93be-6002d160b36a"}], "otherMiddleName": "other", "fundingSources": [{"sourceOfFunding": "Paying for it myself", "sourceOfFundingDisplayName": "Self Funding", "fundingPercentage": "jkhk"}, {"sourceOfFunding": "Company sponsorship", "sourceOfFundingDisplayName": "Company Sponsorship", "fundingPercentage": "kjlk"}], "passportFirstName": "Lucia", "createdAt": "2025-07-15T18:58:42.516Z", "countryDisplayName": "Afghanistan", "visaDocumentUpload": [{"documentId": "3c5447b9-a119-47d8-bcd3-90cd3847d3d3"}], "refereeDetails": [{"refereeInstitution": "jhk", "refereeEmail": "sin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><EMAIL>", "refereeCountry": "IN", "refereeCountryDisplayName": "India", "refereeTitleDisplayName": "Miss", "refereeTitle": "Miss", "refereePosition": "jhk", "refereePhone": {"number": "***********", "numberWithCode": "+1***********", "dialCode": "1", "countryCode": "us"}, "refereeFirstName": "Sindhu", "refereeLastName": "Sindhu"}, {"refereeInstitution": "jkh", "refereeEmail": "sin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><EMAIL>", "refereeCountry": "IN", "refereeCountryDisplayName": "India", "refereeTitleDisplayName": "Mr", "refereeTitle": "Mr", "refereePosition": "li", "refereePhone": {"number": "***********", "numberWithCode": "+1***********", "dialCode": "1", "countryCode": "us"}, "refereeFirstName": "Sindhu", "refereeLastName": "kj"}], "qualification": [{"institutionCountryDisplayName": "Afghanistan", "institutionNameDisplayName": "Other", "institutionCountry": "AF", "subject": "subject", "otherInstitutionName": "other name", "monthCompleted": "2025-07-08", "expectedMonthCompleted": "2025-07-08", "institutionName": "Other", "testCertificateId": [{"documentId": "9d100916-b3c6-452f-b986-1f70d5cd4513"}], "Grade": "93", "qualificationName": "name", "isQualificationCompleted": "Yes"}, {"institutionCountryDisplayName": "Afghanistan", "institutionNameDisplayName": "newgk", "institutionCountry": "AF", "subject": "rewre@", "institutionName": "a28UC000000A4WzYAK", "testCertificateId": [{"documentId": "0d6f8c59-bec0-408a-82f4-32bc961c08d7"}], "Grade": "ew", "qualificationName": "dwdwer", "isQualificationCompleted": "No"}], "progress": 100, "priorEmployments": [{"cv": [{"documentId": "b0e7ef6c-3f10-4cf7-ac7e-aad89a2945b2"}], "endDate": "2025-07-09", "company": "incre", "industry": "it", "department": "it", "employmentStatus": "Full-Time Employment", "employmentStatusDisplayName": "Full Time", "startDate": "2025-07-01", "duties": "sde"}, {"cv": [{"documentId": "424d9b97-66d3-4a18-9593-2110a0483a12"}], "endDate": "2025-07-16", "company": "zoh", "industry": "it", "department": "it", "employmentStatus": "Full-Time Employment", "employmentStatusDisplayName": "Full Time", "startDate": "2025-07-01", "duties": "manger"}], "correspondenceCity": "<PERSON><PERSON><PERSON>", "leadRecordTypeId": "012UC000000YAbFYAW", "teamMembers": [{"teamMemberRole": "Agent Contact", "userId": "FSFEWF", "opportunityAccessLevel": "Edit"}, {"teamMemberRole": "Business Developer", "userId": "SFJHSF", "opportunityAccessLevel": "Edit"}], "uploadPersonalStatement": [{"documentId": "6db41824-8fdf-4bfa-89c9-0fd4538bc003"}], "PK": "<EMAIL>", "studiedInEnglishMedium": "Yes", "applicationId": "0f86293b-8d8e-483c-aafa-ec82e3a3de48", "title": "Mr.", "mobilePhone": {"number": "7229832132", "numberWithCode": "+************", "dialCode": "91", "countryCode": "in"}, "dualNationalityDisplayName": "Antigua and Barbuda", "rplTranscript": [{"documentId": "b7a9c70f-16cb-427c-80c8-b746c050b461"}], "titleDisplayName": "Mr", "genderDisplayName": "Male", "englishMediumYearsDisplayName": "1 year", "studiedInUKDegreeLevel": "Yes", "secondaryMobilePhone": {"number": "**********", "numberWithCode": "+91**********", "dialCode": "91", "countryCode": "in"}, "passportIssuingCountry": "PH", "otherWorkExperienceDetails": "fwefwe", "accountRecordTypeId": "0120O0000007L8XQAU", "legalNationality": "United Kingdom", "city": "Bangalore", "visaTypeDisplayName": "Other", "studentCode": "numvedr", "postalCode": "560102", "provideReferencesNowDisplayName": "Yes", "preferredName": "prefer", "isSuccessfullExtraction": true, "hasRPL": "Yes", "agreeFinalDeclaration": true, "englishTestGrade": "ewfew", "sections": [{"name": "Contact Details", "displayOrder": 2, "status": true}, {"name": "Experience", "displayOrder": 4, "status": true}, {"name": "Funding", "displayOrder": 8, "status": true}, {"name": "Personal Details", "displayOrder": 1, "status": true}, {"name": "Personal Statement", "displayOrder": 5, "status": true}, {"name": "Qualification", "displayOrder": 3, "status": true}, {"name": "Referees", "displayOrder": 7, "status": true}, {"name": "Review & Submit", "displayOrder": 11, "status": false}, {"name": "Submission", "displayOrder": 9, "status": true}, {"name": "Visa", "displayOrder": 6, "status": true}], "passportIssuingCountryDisplayName": "Philippines", "applicationSource": "ARD", "streetAddress": "126, 10th Main Rd, Sector 6, HSR Layout", "noAcademicEvidence": true, "passportLastName": "<PERSON><PERSON><PERSON>", "appId": "ARD0000000028"}
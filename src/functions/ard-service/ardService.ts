import {
  salesforceAgentConfig,
  salesforceAgentSubObjectsConfig,
} from "@functions/ard-service/salesforceconfig";
import { postDataSf } from "src/connectors/salesforce-connectors";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env.REGION });

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);
const loggerEnum = new LoggerEnum();

export const handleArdSfSaveOrUpdateRequests = async (event) => {
  console.log(JSON.stringify(event));
  const brand = "ARD";
  for (let record of event) {
    let applicationDetails = JSON.parse(record["body"]);
    const requestId = applicationDetails.requestId;
    const filledBy = applicationDetails?.applicationFilledBy;
    const APIKEY = applicationDetails.APIKEY;
    const currentUTC = new Date().toISOString();
    let request = {};
    let referenceMappedDetails;
    if (applicationDetails?.privacyPolicyConsent) {
      applicationDetails.privacyPolicyConsent =
        !applicationDetails?.privacyPolicyConsent;
    }
    if (applicationDetails?.haveCriminalConvictions === "Yes") {
      applicationDetails.criminalConvictions = true;
    } else {
      applicationDetails.criminalConvictions = false;
    }
    if (applicationDetails?.requireStudentVisa === "Yes") {
      applicationDetails.visa = true;
    } else {
      applicationDetails.visa = false;
    }
    try {
      if (
        applicationDetails?.applicationStatus === "submitted" &&
        !applicationDetails?.submittedToSf
      ) {
        try {
          applicationDetails.identityDocumentObject = [
            {
              identityNumber: applicationDetails?.passportNumber,
              identityIssueDate: applicationDetails?.passportIssueDate,
              identityExpiryDate: applicationDetails?.passportExpiryDate,
              identityIssuingCountry:
                applicationDetails?.passportIssuingCountryDisplayName,
              identityType: "Passport",
            },
          ];
          if (applicationDetails.passedEnglishTest === "Yes") {
            applicationDetails.languageProficiency = [
              {
                englishQualificationType: applicationDetails?.englishQualificationType,
                englishTestGrade: applicationDetails?.englishTestGrade,
                englishMediumYearsDisplayName: applicationDetails?.englishMediumYearsDisplayName,
              },
            ];
          }
          const mappings = {
            qualification: "educationHistory",
            priorEmployments: "workHistory",
          };
          for (const [sourceKey, targetKey] of Object.entries(mappings)) {
            if (applicationDetails.hasOwnProperty(sourceKey)) {
              applicationDetails[targetKey] = applicationDetails[sourceKey];
            }
          }
          if (!applicationDetails.educationHistory) {
            applicationDetails.educationHistory = [];
          }
          referenceMappedDetails = await opportunityFileReferenceMapping(applicationDetails);

          applicationDetails = referenceMappedDetails.appDetails;
        } catch (error) {
          console.log("Error in mapping application details", error);
          throw error;
        }
      }

      request["sectionLabel"] = applicationDetails.sectionLabel;
      request["email"] = applicationDetails.email;
      request["applicationId"] = applicationDetails.applicationId;
      request["requestId"] = requestId;
      request["messageId"] = record.messageId;
      if (referenceMappedDetails?.institutionData ||
        referenceMappedDetails?.workHisData) {
        const mappedData = await Promise.all([
          referenceMappedDetails?.institutionData
            ? mapSalesforceObject(
              referenceMappedDetails.institutionData,
              filledBy
            )
            : null,
          referenceMappedDetails?.workHisData
            ? mapSalesforceObject(referenceMappedDetails.workHisData, filledBy)
            : null,
          mapSalesforceObject(applicationDetails, filledBy),
        ]);

        const [mappedInstitutionData, mappedWorkHisData, mappedRequest] =
          mappedData;
        await Promise.all([
          mappedInstitutionData &&
          postDataSf(
            { ...request, ...mappedInstitutionData },
            APIKEY,
            filledBy
          ),
          mappedWorkHisData &&
          postDataSf({ ...request, ...mappedWorkHisData }, APIKEY, filledBy),
        ]);
        await postDataSf({ ...request, ...mappedRequest }, APIKEY, filledBy)
      } else {
        request = {
          ...request,
          ...(await mapSalesforceObject(applicationDetails, filledBy)),
        };
        await postDataSf(request, APIKEY, filledBy);
      }
      await cloudWatchLoggerService.log(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        "Successfully saved to salesforce",
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    } catch (error) {
      await cloudWatchLoggerService.error(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.SAVED_TO_SALESFORCE,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        error.errorDetails
          ? typeof error.errorDetails === "string"
            ? error.errorDetails
            : JSON.stringify(error.errorDetails)
          : JSON.stringify(error),
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    }
  }
};

function opportunityFileReferenceMapping(appDetails) {
  if (appDetails && Array.isArray(appDetails.documents)) {
    const documentMap = new Map();
    appDetails.documents.forEach((doc) => {
      documentMap.set(doc.documentId, doc);
    });

    const addReferences = (documentIds, referenceKey, referenceValue, key?) => {
      if (Array.isArray(documentIds)) {
        documentIds.forEach((docId) => {
          const matchingDocument = documentMap.get(docId);
          if (matchingDocument) {
            matchingDocument[referenceKey] = referenceValue;
          }
          if (key === "passportDocument") {
            matchingDocument.isIdentityInformationAccurate =
              appDetails?.isIdentityInformationAccurate === "Yes" ? true : false;
          }
        });
      }
    };
    let institutionData = {
      documents: [],
      educationHistory: [],
    };

    let workHisData = {
      documents: [],
      workHistory: [],
    };
    if (Array.isArray(appDetails.educationHistory)) {
      appDetails.educationHistory.forEach((institution, index) => {
        const institutionOrder = index + 1;
        const academicCertificate = institution.academicCertificate || [];

        const documentIds = [
          ...academicCertificate.map((doc) => doc.documentId),
        ];

        addReferences(
          documentIds,
          "eduReference",
          `@{EducationHistoryRecord__c_${institutionOrder}.id}`
        );

        institutionData.documents.push(
          ...documentIds.map((docId) => documentMap.get(docId)).filter(Boolean)
        );
        institutionData.educationHistory.push(institution);
      });

      appDetails.educationHistory = [];
      appDetails.documents = appDetails.documents.filter(
        (doc) =>
          !institutionData.documents.some(
            (institutionDoc) => institutionDoc.documentId === doc.documentId
          )
      );
    } else {
      console.error("No institutions found in appDetails. Nothing to process.");
    }
    if (Array.isArray(appDetails.workHistory)) {
      appDetails.workHistory.forEach((work, index) => {
        const workOrder = index + 1;
        const cv = work.cv || [];

        const documentIds = [...cv.map((doc) => doc.documentId)];

        addReferences(
          documentIds,
          "testReference",
          `@{WorkHistoryRecord__c_${workOrder}.id}`
        );

        workHisData.documents.push(
          ...documentIds.map((docId) => documentMap.get(docId)).filter(Boolean)
        );
        workHisData.workHistory.push(work);
      });

      appDetails.workHistory = [];
      appDetails.documents = appDetails.documents.filter(
        (doc) =>
          !workHisData.documents.some(
            (workCv) => workCv.documentId === doc.documentId
          )
      );
    } else {
      console.error("No test data found in appDetails. Nothing to process.");
    }
    const categories = [
      {
        key: "testEvidence",
        referenceKey: "testReference",
        referenceValue: "@{LanguageProficiencyRecord__c_1.id}",
      },
      {
        key: "passportDocument",
        referenceKey: "identityReference",
        referenceValue: "@{IdentityInfoRecord__c_1.id}",
      },
    ];

    if (Array.isArray(categories)) {
      categories.forEach(({ key, referenceKey, referenceValue }) => {
        if (Array.isArray(appDetails[key])) {
          const documentIds = appDetails[key].map((doc) => doc.documentId);
          addReferences(documentIds, referenceKey, referenceValue, key);
        }
      });
    }

    console.log("Processed appDetails.documents:", appDetails.documents);

    return { appDetails, institutionData, workHisData };
  } else {
    console.error("Invalid or missing appDetails.documents.");
  }

  return {
    appDetails,
    institutionData: { documents: [], educationHistory: [] },
    workHisData: { documents: [], workHistory: [] },
  };
}

async function mapSalesforceObject(applicationDetails, filledBy): Promise<any> {
  let result = {};
  const salesforceConfig = salesforceAgentConfig
  const salesforceSubObjectsConfig = salesforceAgentSubObjectsConfig
  for (const object in salesforceConfig) {
    if (Array.isArray(salesforceConfig[object])) {
      if (applicationDetails[salesforceConfig[object][0]]) {
        for (const record of applicationDetails[salesforceConfig[object][0]]) {
          let mappedObject = await mapValues(
            record,
            salesforceSubObjectsConfig[salesforceConfig[object][0]]
          );
          if (mappedObject && Object.keys(mappedObject).length > 0) {
            if (!result[object]) {
              result[object] = [];
            }
            result[object].push(mappedObject);
          }
        }
      }
    } else {
      let mappedObject = await mapValues(
        applicationDetails,
        salesforceConfig[object]
      );
      console.log(mappedObject);
      if (mappedObject && Object.keys(mappedObject).length > 0) {
        result[object] = mappedObject;
      }
    }
  }
  return result;
}
async function mapValues(source, mapping) {
  const result = {};

  for (const [sourceKey, targetKey] of Object.entries(mapping)) {
    let value;

    if (sourceKey.includes(",")) {
      // Handle multiple keys separated by commas
      const keys = sourceKey.split(",");
      const values = [];

      for (const key of keys) {
        if (key.includes(".")) {
          // Handle nested properties
          const nestedKeys = key.trim().split(".");
          let nestedValue = source;

          for (const nestedKey of nestedKeys) {
            nestedValue = nestedValue ? nestedValue[nestedKey] : undefined;
          }
          if (nestedValue !== undefined) {
            values.push(nestedValue);
          }
        } else {
          // Handle simple keys
          const simpleValue = source[key.trim()];
          if (simpleValue !== undefined) {
            values.push(simpleValue);
          }
        }
      }

      // Join all collected values into a single string
      value = values.join(" ");
    } else if (sourceKey.includes(".")) {
      // Handle single nested property
      const keys = sourceKey.split(".");
      value = source;

      for (const key of keys) {
        value = value ? value[key] : undefined;
      }
    } else {
      // Handle single simple property
      value = source[sourceKey];
    }

    if (value !== undefined) {
      if (Array.isArray(targetKey)) {
        // If the targetKey is an array, map each value to the corresponding sourceKey
        targetKey.forEach((key) => {
          result[key] = value;
        });
      } else {
        // Otherwise, map the value directly
        result[targetKey] = value;
      }
    }
  }

  return result;
}

export const salesforceAgentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    institutionName: "Additional_Info__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
    isIdentityInformationAccurate: "Is_Info_Accurate__c",
  },
  educationHistory: {
    institutionType: "Institution_Type__c",
    institutionName: "InstitutionName__c",
    institutionCountryDisplayName: "Country__c",
    institutionCity: "City__c",
    institutionState : "Province_State__c",
    levelOfStudy: "Education_Level__c",
    firstEnrolmentDate: "EnrolmentDateYear__c",
    lastEnrolmentDate: "GraduationDate__c",
    fieldOfStudy: "Specialisation__c",
    overallScore: "GPA_Score__c",
    institutionOrder: "Name",
  },
  languageProficiency: {
    additionalLanguageProficiency: "Language_Level__c",
    proficiencyQualification: "ProficiencyQualification__c",
    testDate: "TestDate__c",
    overallScore: "TestScore__c",
    provider: "TestProvider__c"
  },
  workHistory: {
    experienceType: "Employment_Type__c",
    organizationName: "Employer__c",
    workPosition: "NatureOfDuties__c",
    workCountryDisplayName: "EmployerAddress__c",
    workStartDate: "StartDate__c",
    workEndDate: "EndDate__c",
    workFrequency: "Position_Type__c"
  },
  teamMembers: {
    opportunityAccessLevel: "OpportunityAccessLevel",
    teamMemberRole: "TeamMemberRole",
    userId: "UserId",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityIssueDate: "Issue_Date__c",
    identityExpiryDate: "Expiry_Date__c",
    identityType: "Identity_Type__c",
    identityIssuingCountry: "Issuing_Country__c",
  },
};

export const salesforceAgentConfig = {
  IdentityInfoRecord__c: ["identityDocumentObject"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  EducationHistoryRecord__c: ["educationHistory"],
  OpportunityFile__c: ["documents"],
  WorkHistoryRecord__c: ["workHistory"],
  Lead: {
    prefix: "Salutation",
    firstName: "FirstName",
    lastName: "LastName",
    email: "Email",
    birthDate: "DateOfBirth__c",
    "phoneNumber.numberWithCode": "Phone",
    countryDisplayName: ["Country__c", "Country"],
    "otherNumber.numberWithCode": "OtherPhone__c",
    program: "Programme__c",
    birthCountryDisplayName: "CountryOfBirth__c",
    birthPlace: "PlaceOfBirth__c",
    firstLanguage: "Primary_Language__c",
    ethnicity: "ApplicationInfo_Ethnicity__c",
    leadSource: "LeadSource",
    brand: "Brand__c",
    leadRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    agentAccountId: "AgentAccount__c",
    agentContactId: "Agent_Contact__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    privacyPolicyConsent:["HasOptedOutOfMarketingEmailSync__c", "HasOptedOutOfGUSMarketingEmail__c"]
  },
  Account: {
    prefix: "Salutation",
    firstName: "FirstName",
    lastName: "LastName",
    email: ["PersonEmail","Email__c"],
    middleName: "Middle_Name__c",
    "phoneNumber.numberWithCode": "Phone",
    birthDate: "DateOfBirth__c",
    preferredFirstName: "PreferedFirstName__c",
    correspondenceLanguage: "CommunicationLanguage__c",
    countryDisplayName: ["Country__c", "PersonMailingCountry"],
    streetAddress: "PersonMailingStreet",
    city: ["gaconnector_City__c", "PersonMailingCity"],
    state: "PersonMailingState",
    postalCode: "PersonMailingPostalCode",
    correspondenceStreetAddress: "BillingStreet",
    correspondenceCity: "BillingCity",
    correspondencePostalCode: "BillingPostalCode",
    correspondenceState: "BillingState",
    correspondenceCountryDisplayName: "BillingCountry",
    birthPlace: "PlaceOfBirth__c",
    citizenshipDisplayName: "Citizenship__c",
    "otherNumber.numberWithCode": "PrimaryPhone__c",
    birthCountryDisplayName: "CountryOfBirth__c",
    gender: "Gender__c",
    passportNumber: "Passport__pc",
    dualCitizenshipDisplayName: "Dual_Citizenship__c",
    citizenshipStatus: "Citizenship_Status__c",
    firstLanguage: "Primary_Language__c",
    ethnicity: "Ethnicity__c",
    businessUnit: "BusinessUnit__c",
    accountRecordTypeId: "RecordTypeId",
    brand: "Brand__c",
    levelDisplayName: "Level__pc",
    agentContactUserId: "OwnerId",
    privacyPolicyConsent:["HasOptedOutOfMarketingEmail__c", "HasOptedOutOfGUSMarketingEmail__c"]
  },
  Opportunity: {
    miscDetails: "Application_Misc_Details__c",
    program: "Programme__c",
    partnerInstitution: "PathwayProviderId__c",
    "phoneNumber.numberWithCode": "AccountPhone__c",
    location: "Location__c",
    startTerm: ["CloseDate", "Product_Intake_Date__c"],
    citizenshipDisplayName: "Citizenship__c",
    city: "gaconnector_City__c",
    mailingState: "State__c",
    visaType: "VisaType__c",
    declaration3: "DeclarationInfoProvided__c",
    appId: "ApplicationId__c",
    agentContactId: "Agent_Contact__c",
    // agentAccountId: "AgentAccount__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    applicationId: "ApplicationFormId__c",
    programDisplayName: "Name",
    stage: "StageName",
    opportunityApplicationSource: "ApplicationSource__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    pricebookId: "Pricebook2Id",
    admissionStage: "AdmissionsStage__c",
  },
  Application__c: {
    prefix: "Title__c",
    firstName: "First_Name__c",
    lastName: "Last_Name__c",
    countryDisplayName: "Country__c",
    birthDate: "Date_of_birth__c",
    preferredFirstName : "Preferred_Name__c",
    level: "Level_Of_Study__c",
    program: "Programme__c",
    programDisplayName: "Program_Of_Study__c",
    startTerm: "Intake__c",
    middleName: "Middle_Other_Name_s__c",
    email: "Email__c",
    gender: "Gender__c",
    streetAddress: "Street_Address__c",
    city: "City__c",
    postalCode: "PostCode__c",
    "phoneNumber.numberWithCode": "Mobile__c",
    "otherNumber.numberWithCode": "Alternate_Phone_Number__c",
    birthCountryDisplayName: "Country_of_Birth__c",
    birthPlace: "Place_of_Birth__c",
    citizenshipDisplayName: "Citizenship__c",
    dualCitizenshipDisplayName: "Dual_Nationality__c",
    firstLanguage: "Primary_Language__c",
    ethnicity: "Ethnicity__c",
    institutionName :"Institution1__c",
    institutionCountryDisplayName: "Country_of_Study_1__c",
    levelOfStudy: "Level_Of_Study__c",
    fieldOfStudy: "Specialisation_If_Any_1__c",
    organizationName: "Employer_1__c",
    workPosition: "Nature_Of_Duties_1__c",
    workCountryDisplayName: "Employment_Country_1__c",
    workStartDate: "Employment_Start_Date_1__c",
    workEndDate: "Employment_End_Date_1__c",
    workFrequency: "Position_Title_1__c",
    accountManagerUserId: "Business_Developer__c",
    agentContactUserId: "OwnerId",
    applicationId: "Application_Form_Id__c",
    applicationRecordTypeId: "RecordTypeId",
    agentAccountId: "Agent_Account__c",
    agentContactId: "Agent_Contact__c",
    fullName: "Name",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
  OpportunityTeamMember: ["teamMembers"],
};

export const salesforceStudentSubObjectsConfig = {
};

export const salesforceStudentConfig = {
};


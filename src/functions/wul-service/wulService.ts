import {
  salesforceAgentConfig,
  salesforceAgentSubObjectsConfig,
  salesforceStudentConfig,
  salesforceStudentSubObjectsConfig,
} from "@functions/wul-service/salesforceconfig";
import { postDataSf } from "src/connectors/salesforce-connectors";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env.REGION });

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);
const loggerEnum = new LoggerEnum();

export const handleWulSfSaveOrUpdateRequests = async (event) => {
  console.log('Starting handleWulSfSaveOrUpdateRequests with event:', JSON.stringify(event));
  const brand = "WUL";
  console.log(`Processing ${event.length} records for brand: ${brand}`);
  
  for (let record of event) {
    console.log('Processing record:', record.messageId);
    let applicationDetails = JSON.parse(record["body"]);
    const requestId = applicationDetails.requestId;
    const filledBy = applicationDetails?.applicationFilledBy;
    const APIKEY = applicationDetails.APIKEY;
    const currentUTC = new Date().toISOString();
    let request = {};
    let referenceMappedDetails;
    
    console.log(`Application details - RequestId: ${requestId}, FilledBy: ${filledBy}, ApplicationId: ${applicationDetails.applicationId}`);
    console.log(`Application status: ${applicationDetails?.applicationStatus}, SubmittedToSf: ${applicationDetails?.submittedToSf}`);
    
    if (applicationDetails?.privacyPolicyConsent) {
      applicationDetails.privacyPolicyConsent =
        !applicationDetails?.privacyPolicyConsent;
      console.log('Privacy policy consent inverted');
    }
    console.log(
      "Modified Application details document -->",
      applicationDetails.documents
    );
    try {
      if (
        applicationDetails?.applicationStatus === "submitted" &&
        !applicationDetails?.submittedToSf
      ) {
        console.log('Application is submitted and not yet sent to Salesforce - processing mappings');
        try {
          console.log('Creating identity document object from passport details');
          applicationDetails.identityDocumentObject = [
            {
              identityNumber: applicationDetails?.passportNumber,
              identityIssueDate: applicationDetails?.passportIssueDate,
              identityExpiryDate: applicationDetails?.passportExpiryDate,
              identityIssuingCountry:
                applicationDetails?.passportIssuingCountryDisplayName,
              identityType: "Passport",
            },
          ];
          console.log('Identity document object created:', applicationDetails.identityDocumentObject);

          referenceMappedDetails = await opportunityFileReferenceMapping(applicationDetails);

          applicationDetails?.documents.forEach((doc) => {
            if (doc.documentType === 'ID/Passport') {
              doc.isIdentityInformationAccurate =
                applicationDetails.isIdentityInformationAccurate === "Yes";
            }
          });

          applicationDetails = referenceMappedDetails.appDetails;
          const additionalMappings = buildMappings(applicationDetails);
          applicationDetails = {
            ...applicationDetails,
            ...additionalMappings,
          };
        } catch (error) {
          console.log("Error in mapping application details:", error);
          throw error;
        }
      } else {
        console.log('Skipping mapping - application not submitted or already sent to Salesforce');
      }
      applicationDetails.birthPlace = [applicationDetails?.birthCity, applicationDetails?.birthState]
        .filter(Boolean)
        .join(', ');
      
      applicationDetails.citizenshipStatus = applicationDetails?.residencyStatus === true ? "Permanent Residency" : null;
      
      applicationDetails.ethnicity = applicationDetails?.isHispanicOrLatino === "Yes" ? "Hispanic or Latino" : null;
      request["sectionLabel"] = applicationDetails.sectionLabel;
      request["email"] = applicationDetails.email;
      request["applicationId"] = applicationDetails.applicationId;
      request["requestId"] = requestId;
      request["messageId"] = record.messageId;
      console.log(`Request base object created for section: ${applicationDetails.sectionLabel}`);
      
      if (referenceMappedDetails?.institutionData) {
        const mappedInstitutionData = {
          ...request,
          ...(await mapSalesforceObject(
            referenceMappedDetails?.institutionData,
            filledBy
          )),
        };
        request = {
          ...request,
          ...(await mapSalesforceObject(applicationDetails, filledBy)),
        };
        console.log('Sending both institution and application data to Salesforce');
        await Promise.all([
          postDataSf(mappedInstitutionData, APIKEY, filledBy),
          postDataSf(request, APIKEY, filledBy),
        ]);
        console.log('Both requests sent to Salesforce successfully');
      } else {
        console.log('No institution data - processing single mapping');
        console.log('Mapping application details to Salesforce format');
        request = {
          ...request,
          ...(await mapSalesforceObject(applicationDetails, filledBy)),
        };
        console.log('Sending application data to Salesforce');
        await postDataSf(request, APIKEY, filledBy);
        console.log('Request sent to Salesforce successfully');
      }
      await cloudWatchLoggerService.log(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        "Successfully saved to salesforce",
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    } catch (error) {
      await cloudWatchLoggerService.error(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.SAVED_TO_SALESFORCE,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        error.errorDetails
          ? typeof error.errorDetails === "string"
            ? error.errorDetails
            : JSON.stringify(error.errorDetails)
          : JSON.stringify(error),
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    }
  }
};

function opportunityFileReferenceMapping(appDetails) {
  console.log('[WUL-SERVICE] Starting opportunity file reference mapping');
  if (appDetails && Array.isArray(appDetails.documents)) {
    console.log(`[WUL-SERVICE] Processing ${appDetails.documents.length} documents`);
    const documentMap = new Map();
    appDetails.documents.forEach((doc) => {
      documentMap.set(doc.documentId, doc);
    });
    console.log('[WUL-SERVICE] Document map created with', documentMap.size, 'documents');

    const addReferences = (documentIds, referenceKey, referenceValue, key?) => {
      documentIds.forEach((docId) => {
        const matchingDocument = documentMap.get(docId);
        if (matchingDocument) {
          matchingDocument[referenceKey] = referenceValue;
        }
        if (key === "passportDocument") {
          matchingDocument.isIdentityInformationAccurate =
            appDetails?.isIdentityInformationAccurate === "Yes" ? true : false;
        }
      });
    };

    const categories = [
      {
        key: "passportDocument",
        referenceKey: "identityReference",
        referenceValue: "@{IdentityInfoRecord__c_1.id}",
      }
    ];

    categories.forEach(({ key, referenceKey, referenceValue }) => {
      if (Array.isArray(appDetails[key])) {
        const documentIds = appDetails[key].map((doc) => doc.documentId);
        addReferences(documentIds, referenceKey, referenceValue, key);
      } else {
        console.log(`No documents found for category: ${key}`);
      }
    });

    console.log("Processed appDetails.documents:", appDetails.documents);

    return { appDetails };
  } else {
    console.error("Invalid or missing appDetails.documents.");
  }

  return {
    appDetails
  };
}

function buildMappings(applicationDetails) {
  try {
    const {
          institutionType,
          institutionName,
          institutionCountryDisplayName,
          institutionCity,
          institutionState,
          levelOfStudy,
          firstEnrolmentDate,
          lastEnrolmentDate,
          fieldOfStudy,
          overallScore,
          institutionOrder,
          additionalLanguageProficiency,
          proficiencyQualification,
          testDate,
          provider,
          experienceType,
          organizationName,
          workPosition,
          workCountryDisplayName,
          workStartDate,
          workEndDate,
          workFrequency
    } = applicationDetails;

    let mappings = {
      educationHistory: [
        {
          institutionType,
          institutionName,
          institutionCountryDisplayName,
          institutionCity,
          institutionState,
          levelOfStudy,
          firstEnrolmentDate,
          lastEnrolmentDate,
          fieldOfStudy,
          overallScore,
          institutionOrder
        },
      ],
      languageProficiency: [
        {
          additionalLanguageProficiency,
          proficiencyQualification,
          testDate,
          provider
        },
      ],
      workHistory: [
        {
          experienceType,
          organizationName,
          workPosition,
          workCountryDisplayName,
          workStartDate,
          workEndDate,
          workFrequency
        },
      ],
    };
    return mappings;
  } catch (error) {
    console.error('Error building mappings:', error);
    throw error;
  }
}

async function mapSalesforceObject(applicationDetails, filledBy): Promise<any> {
  let result = {};
  const salesforceConfig =
    filledBy === "agent" ? salesforceAgentConfig : salesforceStudentConfig;
  const salesforceSubObjectsConfig =
    filledBy === "agent"
      ? salesforceAgentSubObjectsConfig
      : salesforceStudentSubObjectsConfig;
  
  for (const object in salesforceConfig) {
    if (Array.isArray(salesforceConfig[object])) {
      const subObjectKey = salesforceConfig[object][0];
      if (applicationDetails[subObjectKey]) {
        for (const record of applicationDetails[subObjectKey]) {
          let mappedObject = await mapValues(
            record,
            salesforceSubObjectsConfig[subObjectKey]
          );
          if (mappedObject && Object.keys(mappedObject).length > 0) {
            if (!result[object]) {
              result[object] = [];
            }
            result[object].push(mappedObject);
          }
        }
      } else {
        console.log(`No data found for sub-object key: ${subObjectKey}`);
      }
    } else {
      let mappedObject = await mapValues(
        applicationDetails,
        salesforceConfig[object]
      );
      if (mappedObject && Object.keys(mappedObject).length > 0) {
        result[object] = mappedObject;
      }
    }
  }
  return result;
}
async function mapValues(source, mapping) {
  const result = {};

  for (const [sourceKey, targetKey] of Object.entries(mapping)) {
    let value;

    if (sourceKey.includes(",")) {
      // Handle multiple keys separated by commas
      const keys = sourceKey.split(",");
      const values = [];

      for (const key of keys) {
        if (key.includes(".")) {
          // Handle nested properties
          const nestedKeys = key.trim().split(".");
          let nestedValue = source;

          for (const nestedKey of nestedKeys) {
            nestedValue = nestedValue ? nestedValue[nestedKey] : undefined;
          }
          if (nestedValue !== undefined) {
            values.push(nestedValue);
          }
        } else {
          // Handle simple keys
          const simpleValue = source[key.trim()];
          if (simpleValue !== undefined) {
            values.push(simpleValue);
          }
        }
      }

      // Join all collected values into a single string
      value = values.join(" ");
    } else if (sourceKey.includes(".")) {
      // Handle single nested property
      const keys = sourceKey.split(".");
      value = source;

      for (const key of keys) {
        value = value ? value[key] : undefined;
      }
    } else {
      // Handle single simple property
      value = source[sourceKey];
    }

    if (value !== undefined) {
      if (Array.isArray(targetKey)) {
        // If the targetKey is an array, map each value to the corresponding sourceKey
        targetKey.forEach((key) => {
          result[key] = value;
        });
      } else {
        // Otherwise, map the value directly
        result[targetKey] = value;
      }
    } else {
      console.log(`No value found for source key: ${sourceKey}`);
    }
  }
  return result;
}

{"name": "oap-handlers", "version": "1.0.0", "description": "Serverless aws-nodejs-typescript template", "main": "serverless.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@gus-eip/loggers": "^4.0.9", "@middy/core": "^3.4.0", "@middy/http-json-body-parser": "^3.4.0", "@nestjs/common": "^10.3.7", "aws-jwt-verify": "^4.0.1", "aws-sdk": "^2.1608.0", "axios": "^1.6.5", "axios-retry": "^4.5.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "csv-parser": "^3.0.0", "fs": "^0.0.1-security", "parquetjs": "^0.11.2", "path": "^0.12.7", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "winston": "^3.11.0", "winston-aws-cloudwatch": "^3.0.0", "zlib": "^1.0.5"}, "devDependencies": {"@serverless/typescript": "3.21.0", "@types/node": "14.14.25", "esbuild": "^0.14.11", "json-schema-to-ts": "^1.5.0", "serverless-esbuild": "^1.23.3", "serverless-offline": "^14.4.0", "ts-node": "^10.4.0", "tsconfig-paths": "^3.9.0", "typescript": "^4.1.3"}, "author": "The serverless webpack authors (https://github.com/elastic-coders/serverless-webpack)", "license": "MIT", "repository": "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-handlers"}